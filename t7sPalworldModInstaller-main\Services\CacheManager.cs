using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace ModInstallerApp.Services
{
    public class CacheManager : IDisposable
    {
        private readonly string _cacheDirectory;
        private readonly ConcurrentDictionary<string, object> _memoryCache = new();
        private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();
        private readonly ReaderWriterLockSlim _diskCacheLock = new();
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;
        
        public CacheManager(string appDataPath)
        {
            _cacheDirectory = Path.GetFullPath(Path.Combine(appDataPath, "Cache"));
            Directory.CreateDirectory(_cacheDirectory);
            
            // Setup periodic cleanup timer (every hour)
            _cleanupTimer = new Timer(PerformCleanup, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
        }

        public void Set<T>(string key, T value, TimeSpan? expiry = null)
        {
            ThrowIfDisposed();
            
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("Cache key cannot be null or empty", nameof(key));
            
            var expiryTime = DateTime.Now.Add(expiry ?? TimeSpan.FromHours(1));
            
            _memoryCache.AddOrUpdate(key, value!, (k, v) => value!);
            _cacheTimestamps.AddOrUpdate(key, expiryTime, (k, v) => expiryTime);
            
            // Also persist to disk for important data
            if (IsImportantCache(key))
            {
                _ = Task.Run(() => PersistToDiskAsync(key, value));
            }
        }

        public T? Get<T>(string key)
        {
            ThrowIfDisposed();
            
            if (string.IsNullOrWhiteSpace(key))
                return default(T);

            // Check if cache expired
            if (_cacheTimestamps.TryGetValue(key, out var expiry) && DateTime.Now > expiry)
            {
                InvalidateCache(key);
                return default(T);
            }

            // Check memory cache first
            if (_memoryCache.TryGetValue(key, out var cached))
            {
                if (cached is T typedValue)
                    return typedValue;
            }

            // Try loading from disk
            return LoadFromDiskAsync<T>(key).GetAwaiter().GetResult();
        }

        public async Task<T?> GetAsync<T>(string key)
        {
            ThrowIfDisposed();
            
            if (string.IsNullOrWhiteSpace(key))
                return default(T);

            // Check if cache expired
            if (_cacheTimestamps.TryGetValue(key, out var expiry) && DateTime.Now > expiry)
            {
                InvalidateCache(key);
                return default(T);
            }

            // Check memory cache first
            if (_memoryCache.TryGetValue(key, out var cached))
            {
                if (cached is T typedValue)
                    return typedValue;
            }

            // Try loading from disk
            return await LoadFromDiskAsync<T>(key);
        }

        public void InvalidateCache(string key)
        {
            ThrowIfDisposed();
            
            if (string.IsNullOrWhiteSpace(key))
                return;

            _memoryCache.TryRemove(key, out _);
            _cacheTimestamps.TryRemove(key, out _);
            
            var diskPath = GetDiskCachePath(key);
            if (File.Exists(diskPath))
            {
                try
                {
                    File.Delete(diskPath);
                }
                catch
                {
                    // Ignore deletion errors
                }
            }
        }

        public void ClearAll()
        {
            ThrowIfDisposed();
            
            _memoryCache.Clear();
            _cacheTimestamps.Clear();
            
            _diskCacheLock.EnterWriteLock();
            try
            {
                if (Directory.Exists(_cacheDirectory))
                {
                    foreach (var file in Directory.GetFiles(_cacheDirectory, "*.cache"))
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // Ignore deletion errors
                        }
                    }
                }
            }
            finally
            {
                _diskCacheLock.ExitWriteLock();
            }
        }

        public void ClearExpired()
        {
            ThrowIfDisposed();
            
            var now = DateTime.Now;
            var expiredKeys = _cacheTimestamps
                .Where(kvp => kvp.Value <= now)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                InvalidateCache(key);
            }
        }

        public int GetCacheSize()
        {
            ThrowIfDisposed();
            return _memoryCache.Count;
        }

        public IReadOnlyDictionary<string, DateTime> GetCacheTimestamps()
        {
            ThrowIfDisposed();
            return new Dictionary<string, DateTime>(_cacheTimestamps);
        }

        private bool IsImportantCache(string key)
        {
            var importantKeys = new[] { "auto_detected_installations", "mod_scan_results", "ue4ss_status", "palschema_status" };
            return importantKeys.Any(k => key.Contains(k, StringComparison.OrdinalIgnoreCase));
        }

        private async Task PersistToDiskAsync<T>(string key, T value)
        {
            try
            {
                var json = JsonSerializer.Serialize(value, new JsonSerializerOptions { WriteIndented = true });
                var path = GetDiskCachePath(key);
                
                _diskCacheLock.EnterWriteLock();
                try
                {
                    await File.WriteAllTextAsync(path, json);
                }
                finally
                {
                    _diskCacheLock.ExitWriteLock();
                }
            }
            catch
            {
                // Ignore disk cache errors
            }
        }

        private async Task<T?> LoadFromDiskAsync<T>(string key)
        {
            try
            {
                var path = GetDiskCachePath(key);
                
                _diskCacheLock.EnterReadLock();
                try
                {
                    if (File.Exists(path))
                    {
                        var json = await File.ReadAllTextAsync(path);
                        var value = JsonSerializer.Deserialize<T>(json);
                        
                        if (value != null)
                        {
                            // Update memory cache
                            _memoryCache.TryAdd(key, value);
                        }
                        
                        return value;
                    }
                }
                finally
                {
                    _diskCacheLock.ExitReadLock();
                }
            }
            catch
            {
                // Ignore disk cache errors
            }
            return default(T);
        }

        private string GetDiskCachePath(string key)
        {
            // Sanitize key for filename
            var sanitizedKey = string.Join("_", key.Split(Path.GetInvalidFileNameChars()));
            return Path.Combine(_cacheDirectory, $"{sanitizedKey}.cache");
        }

        private void PerformCleanup(object? state)
        {
            try
            {
                ClearExpired();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(CacheManager));
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _cleanupTimer?.Dispose();
                _diskCacheLock?.Dispose();
                _disposed = true;
            }
        }

        ~CacheManager()
        {
            Dispose(false);
        }
    }
}